package com.lovebeats.firebase.cloudFunctions

import android.content.Context
import com.google.firebase.functions.FirebaseFunctions
import com.lovebeats.utils.Utils
import timber.log.Timber

object AppToCloudLogger {

    enum class LogLevel(val value: String) {
        LEVEL_ERROR ("ERROR"),
        LEVEL_INFO ("INFO"),
        LEVEL_WARN ("WARN")
    }

    fun logToCloud(context: Context, externalId: String, level: LogLevel, message: String, phone: String?,  stackTrace: String? = null) {
        try {
            Utils.getDeviceDataForLogging(context) { deviceData ->
                val functions = FirebaseFunctions.getInstance()
                val data = hashMapOf(
                    "externalId" to externalId,
                    "level" to level.value,
                    "message" to ("$message --- $deviceData"),
                    "phone" to phone,
                    "stackTrace" to stackTrace
                )
                functions
                    .getHttpsCallable("appToCloudLogger")
                    .call(data)
            }
        }catch (exception: Exception) {
            Timber.e("Exception in appToCloudLogger API: $exception")
        }
    }
}