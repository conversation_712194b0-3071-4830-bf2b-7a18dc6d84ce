package com.lovebeats.firebase.config

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import timber.log.Timber

class RemoteConfigurationService {

    companion object {

        const val IS_APP_LOCATION_RESTRICTED = "IS_APP_LOCATION_RESTRICTED"
        const val BETA_INVITE_MESSAGE = "BETA_INVITE_MESSAGE"
        const val APP_FORCE_UPDATE_VERSION = "ANDROID_APP_FORCE_UPDATE_VERSION"

        private val mFirebaseRemoteConfig = FirebaseRemoteConfig.getInstance()

        fun initialize() {
            loadDefaultValues()
            fetchConfig {}
        }

        private fun loadDefaultValues() {
            // Important: Don't forget to add default values to this map for the remote config keys
            val remoteConfigDefaultsMap = hashMapOf<String, Any>()
            remoteConfigDefaultsMap[IS_APP_LOCATION_RESTRICTED] = "true"
            remoteConfigDefaultsMap[BETA_INVITE_MESSAGE] = "You've been invited to an exclusive first look of LoveBeats. Here's the link to join https://lovebeats.co/"
            mFirebaseRemoteConfig.setDefaultsAsync(remoteConfigDefaultsMap)
        }

        fun fetchConfig(settingNameKey: String? = null,
                        callback: (String?) -> Unit) {

            val expirationDuration = 7200L // 2 hours in seconds

            // TimeInterval is set to expirationDuration here, indicating the next fetch request will use
            // data fetched from the Remote Config service, rather than cached parameter values, if cached
            // parameter values are more than expirationDuration seconds old.
            mFirebaseRemoteConfig.fetch(expirationDuration)
                    .addOnCompleteListener { fetchTask ->

                        if (fetchTask.isSuccessful) {

                            mFirebaseRemoteConfig.activate()
                                    .addOnCompleteListener { activateTask ->

                                        if (activateTask.exception != null) {

                                            Timber.d("Remote Config not activated Error: ${activateTask.exception}")
                                            // Note: No need to record this as failure here means the fetched config is already activated.
                                        }
                                    }
                        } else if ((fetchTask.exception != null)) {

                            Timber.d("Remote Config not fetched. Error: ${fetchTask.exception}")
                        }

                        var configValue: String? = null

                        if (!settingNameKey.isNullOrEmpty()) {

                            // Firebase Remote Config fetches ALL values as Strings by default and only then maps them to
                            // other types in convenience methods such as getBoolean(), getLong() etc.
                            // getting the values by default type to avoid cast exceptions.
                            configValue = mFirebaseRemoteConfig.getString(settingNameKey)
                        }
                        callback(configValue)
                    }
        }
    }
}
