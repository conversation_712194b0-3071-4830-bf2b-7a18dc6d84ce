package com.lovebeats.services

import android.content.Context
import com.google.firebase.auth.FirebaseAuth
import com.lovebeats.deepLinking.PromotionsHandler
import com.lovebeats.glide.MyAppGlideModule
import com.lovebeats.models.UserObject
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.Constants

class AuthService {

    companion object {

        fun cleanup(context: Context?) {

            val influencerId = AccountPreferences.getInstance(context).getStringValue(Constants.influencerId, "")
            val influencerName = AccountPreferences.getInstance(context).getStringValue(Constants.influencerName, "")
            val source = AccountPreferences.getInstance(context).getStringValue(Constants.source, "")

            AccountPreferences.getInstance(context).clear()
            UserObject.cleanup()

            if (!influencerId.isNullOrEmpty() && context != null) {
                PromotionsHandler.handleInfluencerSignups(context, influencerId, influencerName)
            }

            if (!source.isNullOrEmpty() && context != null) {
                PromotionsHandler.handleBranchCampaignSignups(context, source, influencerId, influencerName)
            }

            context?.let {
                MyAppGlideModule.clearCache(it)
            }
        }

        fun isUserAuthenticated(): Boolean {
            return FirebaseAuth.getInstance().currentUser != null
        }
    }
}