package com.lovebeats.ui.paidVersion.likesYou

import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.databinding.ItemLikesYouBinding
import com.lovebeats.databinding.ItemLikesYouLockedBinding
import com.lovebeats.glide.ImageLoaderModule
import com.lovebeats.models.LikesTabUser
import com.lovebeats.models.UserObject
import com.lovebeats.subscriptions.views.SubscriptionActivity
import com.lovebeats.ui.SpacesImagesViewModel
import com.lovebeats.ui.home.browseProfiles.OtherUserProfileModal
import com.lovebeats.ui.paidVersion.likesYou.LikesYouAdapter.Companion.EXTRA_LIKES_YOU_USER
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.Utils

class LikesYouAdapter(private val context: Activity, private val spacesImagesViewModel: SpacesImagesViewModel) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val EXTRA_LIKES_YOU_USER = "EXTRA_LIKES_YOU_USER"
        const val EXTRA_PASSED_USER = "EXTRA_PASSED_USER"
        const val VIEW_TYPE_UNLOCKED = 0
        const val VIEW_TYPE_LOCKED = 1
    }

    private var likesYourUsersList = arrayListOf<LikesTabUser>()

    override fun getItemViewType(position: Int): Int {
        return if (UserObject.isLoveBeatPlusUser == true) {
            VIEW_TYPE_UNLOCKED
        } else {
            VIEW_TYPE_LOCKED
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_UNLOCKED -> {
                val itemBinding = ItemLikesYouBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                itemBinding.userProfilePhoto.clipToOutline = true
                LikesYouUserViewHolder(itemBinding)
            }
            VIEW_TYPE_LOCKED -> {
                val itemBinding = ItemLikesYouLockedBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                itemBinding.userProfilePhoto.clipToOutline = true
                LikesYouLockedViewHolder(itemBinding)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun getItemCount(): Int = likesYourUsersList.size

    override fun onBindViewHolder(viewHolder: RecyclerView.ViewHolder, position: Int) {
        when (viewHolder) {
            is LikesYouUserViewHolder -> {
                viewHolder.bindView(likesYourUsersList[position], context, spacesImagesViewModel)
            }
            is LikesYouLockedViewHolder -> {
                viewHolder.bindView(likesYourUsersList[position], context, spacesImagesViewModel)
            }
        }
    }

    fun setLikesYouUsersList(likesTabUsersMap: ArrayList<LikesTabUser>) {
        this.likesYourUsersList = likesTabUsersMap
        notifyDataSetChanged()
    }
}

class LikesYouUserViewHolder(private val itemBinding: ItemLikesYouBinding) : RecyclerView.ViewHolder(itemBinding.root) {

    fun bindView(likesTabUser: LikesTabUser, context: Activity, spacesImagesViewModel: SpacesImagesViewModel) {

        val userHeight = Utils.heightInFeetFromInchesWithQuotes(likesTabUser.height.toString().toDouble())

        itemBinding.userName.text = likesTabUser.name.toString()
        itemBinding.userHeight.text = userHeight

        val width = (AppUtils.getDpForImages(context, 156f))
        val height = AppUtils.getDpForImages(context, 156f)
        ImageLoaderModule.loadImageIntoImageViewNoCropWithLoadingAndCallback(context,spacesImagesViewModel,
                "${likesTabUser.likedYouUserFirebaseId}/${Constants.photoFileName1}",itemBinding.userProfilePhoto,
                shouldCacheImage = true, width = width, height = height)

        itemBinding.userProfilePhoto.setOnClickListener {

            val intent = Intent(context, OtherUserProfileModal::class.java)
            intent.putExtra(EXTRA_LIKES_YOU_USER, likesTabUser)
            context.startActivity(intent)
        }
    }
}

class LikesYouLockedViewHolder(private val itemBinding: ItemLikesYouLockedBinding) : RecyclerView.ViewHolder(itemBinding.root) {

    fun bindView(likesTabUser: LikesTabUser, context: Activity, spacesImagesViewModel: SpacesImagesViewModel) {

        val width = (AppUtils.getDpForImages(context, 156f))
        val height = AppUtils.getDpForImages(context, 156f)

        // Load the image but it will be blurred by the overlay
        ImageLoaderModule.loadImageIntoImageViewNoCropWithLoadingAndCallback(context, spacesImagesViewModel,
                "${likesTabUser.likedYouUserFirebaseId}/${Constants.photoFileName1}", itemBinding.userProfilePhoto,
                shouldCacheImage = true, width = width, height = height)

        // Click handler to show subscription screen
        itemBinding.root.setOnClickListener {
            val intent = Intent(context, SubscriptionActivity::class.java)
            intent.putExtra(SubscriptionActivity.SUBSCRIPTION_ORIGIN, SubscriptionActivity.LIKES_YOU_LOCKED)
            context.startActivity(intent)
        }
    }
}