<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:alpha="0.8">

    <!-- Blurred profile photo with overlay -->
    <FrameLayout
        android:id="@+id/photoContainer"
        android:layout_width="match_parent"
        android:layout_height="156dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/userProfilePhoto"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:background="@drawable/rounded_outline" />

        <!-- Blur overlay -->
        <View
            android:id="@+id/blurOverlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/rounded_outline"
            android:backgroundTint="#80000000" />

        <!-- Lock icon -->
        <ImageView
            android:id="@+id/lockIcon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_lock_white_24dp"
            android:tint="@color/white" />

    </FrameLayout>

    <!-- Hidden placeholder text -->
    <TextView
        android:id="@+id/hiddenText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:fontFamily="@font/inter_medium"
        android:text="Hidden – Upgrade to See"
        android:textAlignment="center"
        android:textColor="@color/grey2"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="@id/photoContainer"
        app:layout_constraintStart_toStartOf="@id/photoContainer"
        app:layout_constraintTop_toBottomOf="@+id/photoContainer" />

</androidx.constraintlayout.widget.ConstraintLayout>
