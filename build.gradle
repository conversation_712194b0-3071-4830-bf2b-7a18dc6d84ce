// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '2.1.0'

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.12.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.0"
        classpath 'com.google.gms:google-services:4.4.3'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.6'
        classpath 'com.google.firebase:perf-plugin:2.0.1'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        maven {
            url "https://jitpack.io"
        }
        maven { url "https://maven.google.com" }
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
